
import React, { useState, useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { apiClient } from '@/lib/apiClient';
import GlobalLoader from '@/components/GlobalLoader';

const AdminAccessGuard = ({ children }) => {
  const [isAuthorized, setIsAuthorized] = useState(null);
  const location = useLocation();

  useEffect(() => {
    const checkAuthorization = async () => {
      if (location.pathname === '/admin/login') {
        setIsAuthorized(true);
        return;
      }

      try {
        const result = await apiClient.checkAdminStatus();
        setIsAuthorized(result.isAdmin);
      } catch (error) {
        setIsAuthorized(false);
      }
    };

    checkAuthorization();
  }, [location.pathname]);

  if (isAuthorized === null) {
    return <GlobalLoader />;
  }

  if (!isAuthorized) {
    return <Navigate to="/" replace />;
  }

  return children;
};

export default AdminAccessGuard;
