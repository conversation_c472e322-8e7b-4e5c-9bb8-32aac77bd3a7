
const API_BASE_URL = '/.netlify/functions';

class ApiClient {
  constructor() {
    this.timeout = 10000; 
    this.allowedActions = new Set([
      'trackUser', 'checkUserStatus', 'checkAdminStatus', 'adminLogin',
      'test', 'getMyHwid', 'getAdminKeys', 'getScriptRequests',
      'updateRequestStatus', 'createScript', 'updateScript', 'deleteScript',
      'getScripts', 'rateScript', 'getUserRatings', 'getAdminStats',
      'submitScriptRequest', 'getPublicScriptRequests'
    ]);
  }

  /**
   * 
   * @param {string} action - The API action to perform
   * @param {object} params - Parameters for the action
   * @returns {Promise<object>} API response
   */
  async request(action, params = {}) {
    if (!action || typeof action !== 'string') {
      throw new Error('Invalid action parameter');
    }

    if (!this.allowedActions.has(action)) {
      throw new Error('Action not allowed');
    }

    const sanitizedParams = this._sanitizeParams(params);

    const url = `${API_BASE_URL}/getUserData`;
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const config = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action, params: sanitizedParams }),
        signal: controller.signal
      };

      const response = await fetch(url, config);
      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`Request failed: ${response.status}`);
      }

      const result = await response.json();

      if (result.error) {
        throw new Error(result.error);
      }

      return result;
    } catch (error) {
      clearTimeout(timeoutId);

      if (error.name === 'AbortError') {
        throw new Error('Request timeout');
      }

      throw new Error('Request failed');
    }
  }

  /**
   * 
   * @param {object} params - Raw parameters
   * @returns {object} Sanitized parameters
   */
  _sanitizeParams(params) {
    if (!params || typeof params !== 'object') {
      return {};
    }

    const sanitized = {};
    for (const [key, value] of Object.entries(params)) {
      if (typeof value === 'string') {
        sanitized[key] = value.replace(/[<>\"']/g, '');
      } else if (typeof value === 'number' || typeof value === 'boolean') {
        sanitized[key] = value;
      } else if (value === null || value === undefined) {
        sanitized[key] = value;
      }
    }

    return sanitized;
  }

  async trackUser(path) {
    return this.request('trackUser', { path });
  }

  async checkUserStatus() {
    return this.request('checkUserStatus', {});
  }

  async checkAdminStatus() {
    return this.request('checkAdminStatus', {});
  }

  async adminLogin(accessKey) {
    return this.request('adminLogin', { accessKey });
  }

  async test() {
    return this.request('test');
  }

  async getMyHwid() {
    return this.request('getMyHwid');
  }

  async getAdminKeys() {
    return this.request('getAdminKeys', {});
  }

  async getScriptRequests() {
    return this.request('getScriptRequests', {});
  }

  async updateRequestStatus(requestId, status) {
    return this.request('updateRequestStatus', { requestId, status });
  }

  async createScript(scriptData) {
    return this.request('createScript', { scriptData });
  }

  async updateScript(scriptId, scriptData) {
    return this.request('updateScript', { scriptId, scriptData });
  }

  async deleteScript(scriptId) {
    return this.request('deleteScript', { scriptId });
  }

  async getScripts() {
    return this.request('getScripts');
  }

  async rateScript(scriptId, rating) {
    return this.request('rateScript', { scriptId, rating });
  }

  async getUserRatings() {
    return this.request('getUserRatings', {});
  }

  async getAdminStats() {
    return this.request('getAdminStats', {});
  }

  async submitScriptRequest(gameName, gameLink, scriptDescription, discordUsername) {
    return this.request('submitScriptRequest', {
      gameName,
      gameLink,
      scriptDescription,
      discordUsername
    });
  }
}

export const apiClient = new ApiClient();
