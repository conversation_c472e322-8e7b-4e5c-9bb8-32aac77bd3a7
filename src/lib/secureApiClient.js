
class SecureApiClient {
  constructor() {
    this.baseURL = '/.netlify/functions';
    this.timeout = 10000; 
    this.requestCount = new Map(); 
    this.maxRequestsPerMinute = 60;
  }

  /**
   * @param {string} action - API action
   * @param {object} params - Request parameters
   * @param {object} options - Additional options
   * @returns {Promise<object>} API response
   */
  async request(action, params = {}, options = {}) {
    if (!this._checkRateLimit()) {
      throw new Error('Rate limit exceeded');
    }

    if (!this._validateInput(action, params)) {
      throw new Error('Invalid request parameters');
    }

    const url = `${this.baseURL}/getUserData`;
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const config = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest', 
          ...options.headers
        },
        body: JSON.stringify({
          action,
          params: this._sanitizeParams(params),
          timestamp: Date.now() 
        }),
        signal: controller.signal,
        credentials: 'same-origin' 
      };

      const response = await fetch(url, config);
      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error('Request failed');
      }

      const result = await response.json();

      if (result.error) {
        throw new Error(result.error);
      }

      return result;
    } catch (error) {
      clearTimeout(timeoutId);

      if (error.name === 'AbortError') {
        throw new Error('Request timeout');
      }

      throw new Error('Request failed');
    }
  }

  /**
   * @returns {boolean} Whether request is allowed
   */
  _checkRateLimit() {
    const now = Date.now();
    const windowStart = now - 60000; 

    for (const [timestamp] of this.requestCount.entries()) {
      if (timestamp < windowStart) {
        this.requestCount.delete(timestamp);
      }
    }

    if (this.requestCount.size >= this.maxRequestsPerMinute) {
      return false;
    }

    this.requestCount.set(now, true);
    return true;
  }

  /**
   * @param {string} action - API action
   * @param {object} params - Parameters
   * @returns {boolean} Whether input is valid
   */
  _validateInput(action, params) {
    if (!action || typeof action !== 'string' || action.length > 50) {
      return false;
    }

    if (params && typeof params !== 'object') {
      return false;
    }

    return true;
  }

  /**
   * @param {object} params - Raw parameters
   * @returns {object} Sanitized parameters
   */
  _sanitizeParams(params) {
    if (!params || typeof params !== 'object') {
      return {};
    }

    const sanitized = {};
    for (const [key, value] of Object.entries(params)) {
      if (typeof value === 'string' && value.length <= 1000) {
        sanitized[key] = value.replace(/[<>\"'&]/g, '').trim();
      } else if (typeof value === 'number' && isFinite(value)) {
        sanitized[key] = value;
      } else if (typeof value === 'boolean') {
        sanitized[key] = value;
      }
    }

    return sanitized;
  }

  async trackUser(path) {
    if (!path || typeof path !== 'string') {
      throw new Error('Invalid path parameter');
    }
    return this.request('trackUser', { path });
  }

  async checkUserStatus() {
    return this.request('checkUserStatus', {});
  }

  async adminRequest(action, params = {}, authToken) {
    if (!authToken) {
      throw new Error('Authentication required');
    }
    
    return this.request(action, params, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });
  }
}

export const secureApiClient = new SecureApiClient();
